package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1.TaxPayerInfoScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoScreen
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step3.CustomsFeeInfoStep3Screen

@Composable
fun CustomsFeeInfoMainScreen(navController: NavHostController) {

    val viewModel: CustomsFeeInfoMainViewModel = hiltViewModel()

    BaseMVIScreen(viewModel = viewModel) { uiState, _ ->
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                when (uiState.currentStep) {
                    1 -> TaxPayerInfoScreen(viewModel, navController) {
                        StepProgressBar(uiState)
                    }
                    2 -> TaxPaymentInfoScreen(viewModel, navController) {
                        StepProgressBar(uiState)
                    }
                    3 -> CustomsFeeInfoStep3Screen()
                }
            }
        }
    }
}
