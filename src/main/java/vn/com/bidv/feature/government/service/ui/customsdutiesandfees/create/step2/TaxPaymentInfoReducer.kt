package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO

class TaxPaymentInfoReducer: Reducer<TaxPaymentInfoUiState, TaxPaymentInfoViewEvent, TaxPaymentInfoSideEffect> {
    override fun reduce(
        previousState: TaxPaymentInfoUiState,
        event: TaxPaymentInfoViewEvent
    ): Pair<TaxPaymentInfoUiState, TaxPaymentInfoSideEffect?> {
        return when (event) {
            is TaxPaymentInfoViewEvent.InitScreen -> {
                previousState to TaxPaymentInfoSideEffect.InitScreen
            }
            is TaxPaymentInfoViewEvent.InitScreenSuccess -> {
                previousState.copy(
                    isInitialized = true,
                    listTreasury = event.treasuryList,
                    listRevenueAuthority = event.revenueAuthorityList,
                    listRevenueAccount = event.revenueAccountList,
                    listAdministrativeArea = event.administrativeAreaList
                ) to null
            }
            is TaxPaymentInfoViewEvent.UpdateFormField -> {
                // Handle form field updates
                previousState to null
            }
            is TaxPaymentInfoViewEvent.UpdateNoteToApproverString -> {
                previousState.copy(
                    noteToApprover = event.value
                ) to null
            }
        }
    }
}

data class TaxPaymentInfoUiState(
    val isInitialized: Boolean = false,
    val listTreasury: List<TreasuryDMO> = emptyList(),
    val listRevenueAuthority: List<RevenueAuthorityDMO> = emptyList(),
    val listRevenueAccount: List<RevenueAccountDMO> = emptyList(),
    val listAdministrativeArea: List<AdministrativeAreaDMO> = emptyList(),
    val treasurySelected: TreasuryDMO? = null,
    val revenueAuthoritySelected: RevenueAuthorityDMO? = null,
    val revenueAccountSelected: RevenueAccountDMO? = null,
    val administrativeAreaSelected: AdministrativeAreaDMO? = null,
    val showTreasuryBottomSheet: Boolean = false,
    val showRevenueAuthorityBottomSheet: Boolean = false,
    val showRevenueAccountBottomSheet: Boolean = false,
    val showAdministrativeAreaBottomSheet: Boolean = false,

    val noteToApprover: String = ""
): ViewState

enum class TaxPaymentFormField {
    TREASURY_CODE, REV_AUTH_CODE, REV_ACC_CODE, ADM_AREA_CODE,
}

sealed interface TaxPaymentInfoViewEvent: ViewEvent {
    data object InitScreen: TaxPaymentInfoViewEvent
    data class InitScreenSuccess(
        val treasuryList: List<TreasuryDMO>,
        val revenueAuthorityList: List<RevenueAuthorityDMO>,
        val revenueAccountList: List<RevenueAccountDMO>,
        val administrativeAreaList: List<AdministrativeAreaDMO>
    ): TaxPaymentInfoViewEvent
    data class UpdateFormField(val field: TaxPaymentFormField, val value: String): TaxPaymentInfoViewEvent
    data class UpdateNoteToApproverString(val value: String): TaxPaymentInfoViewEvent
    sealed class ToggleBottomSheet: TaxPaymentInfoViewEvent {
        data class Treasury(val show: Boolean): ToggleBottomSheet()
        data class RevenueAuthority(val show: Boolean): ToggleBottomSheet()
        data class RevenueAccount(val show: Boolean): ToggleBottomSheet()
        data class AdministrativeArea(val show: Boolean): ToggleBottomSheet()
    }
}

sealed interface TaxPaymentInfoSideEffect: SideEffect {
    data object InitScreen: TaxPaymentInfoSideEffect
}