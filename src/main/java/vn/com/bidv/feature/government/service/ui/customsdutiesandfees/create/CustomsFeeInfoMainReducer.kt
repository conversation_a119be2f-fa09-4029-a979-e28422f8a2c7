package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.model.BaseTransaction

class CustomsFeeInfoMainReducer: Reducer<CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEffect> {

    @Immutable
    data class CustomsFeeInfoMainViewState(
        val currentStep: Int = 0,
        val listPayment: List<BaseTransaction> = emptyList()
    ): ViewState

    @Immutable
    sealed class CustomsFeeInfoMainViewEvent: ViewEvent {
        data class ChangeStep(val step: Int): CustomsFeeInfoMainViewEvent()
        data class ChangeToStepTwo(val listPayment: List<BaseTransaction>): CustomsFeeInfoMainViewEvent()
    }

    @Immutable
    sealed class CustomsFeeInfoMainViewEffect(): SideEffect

    override fun reduce(
        previousState: CustomsFeeInfoMainViewState,
        event: CustomsFeeInfoMainViewEvent
    ): Pair<CustomsFeeInfoMainViewState, CustomsFeeInfoMainViewEffect?> {

        return when(event) {
            is CustomsFeeInfoMainViewEvent.ChangeStep -> {
                previousState.copy(currentStep = event.step) to null
            }
            is CustomsFeeInfoMainViewEvent.ChangeToStepTwo -> {
                previousState.copy(currentStep = 1, listPayment = event.listPayment) to null
            }
        }
    }

}